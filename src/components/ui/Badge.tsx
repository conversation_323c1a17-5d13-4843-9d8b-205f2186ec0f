import React from 'react';

// 统一 Badge，用于标签/技术栈等
// Confirmed via mcp-feedback-enhanced
export interface BadgeProps {
  children: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'neutral';
  variant?: 'primary' | 'secondary' | 'outline' | 'soft'; // 新增 variant 支持
  size?: 'sm' | 'md' | 'lg'; // 新增 size 支持
  soft?: boolean; // 是否使用柔和色块
  className?: string;
}

export default function Badge({
  children,
  color = 'primary',
  variant,
  size = 'md',
  soft = true,
  className = ''
}: BadgeProps) {
  // 尺寸映射
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-xs px-2.5 py-1',
    lg: 'text-sm px-3 py-1.5',
  };

  // 基础类
  const base = `inline-flex items-center rounded-full font-medium transition-colors duration-200 ${sizeClasses[size]}`;

  // 如果使用 variant，优先使用 variant 样式
  if (variant) {
    const variantMap: Record<string, string> = {
      primary: 'bg-primary-600 text-white',
      secondary: 'bg-secondary-600 text-white',
      outline: 'border border-neutral-300 text-neutral-700 bg-transparent hover:bg-neutral-50',
      soft: 'bg-primary-50 text-primary-700 ring-1 ring-primary-100',
    };
    return <span className={`${base} ${variantMap[variant]} ${className}`.trim()}>{children}</span>;
  }

  // 否则使用原有的 color + soft 逻辑
  const colorMap: Record<string, string> = soft ? {
    primary: 'bg-primary-50 text-primary-700 ring-1 ring-primary-100',
    secondary: 'bg-secondary-50 text-secondary-700 ring-1 ring-secondary-100',
    accent: 'bg-accent-50 text-accent-700 ring-1 ring-accent-100',
    neutral: 'bg-neutral-100 text-neutral-700 ring-1 ring-neutral-200',
  } : {
    primary: 'bg-primary-600 text-white',
    secondary: 'bg-secondary-600 text-white',
    accent: 'bg-accent-600 text-white',
    neutral: 'bg-neutral-700 text-white',
  };

  return <span className={`${base} ${colorMap[color]} ${className}`.trim()}>{children}</span>;
}

