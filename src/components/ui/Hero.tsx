import React from 'react';


// 统一 Hero 组件：白底 + 中性文案 + 可选操作区
// 符合 Apple HIG 的克制留白与层级
// Confirmed via mcp-feedback-enhanced
interface HeroProps {
  title: React.ReactNode;
  subtitle?: React.ReactNode;
  align?: 'left' | 'center';
  className?: string;
  actions?: React.ReactNode; // CTA 按钮区
}

export default function Hero({ title, subtitle, align = 'left', className = '', actions }: HeroProps) {
  const alignCls = align === 'center' ? 'text-center' : 'text-left';
  const actionsWrapCls = align === 'center' ? 'justify-center' : 'justify-start';
  return (
    <section className={`bg-white py-16 ${className}`.trim()}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`${alignCls}`}>
          <h1 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
            {title}
          </h1>
          {subtitle && (
            <p className="text-lg md:text-xl text-neutral-600 max-w-3xl inline-block">
              {subtitle}
            </p>
          )}
          {actions && (
            <div className={`mt-6 flex flex-col sm:flex-row gap-4 ${actionsWrapCls}`}>
              {actions}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

