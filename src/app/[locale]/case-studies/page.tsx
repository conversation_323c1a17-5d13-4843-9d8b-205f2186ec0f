'use client';
import React, { useState } from 'react';
import { motion } from "framer-motion";
import { useParams } from 'next/navigation';
import { type Locale } from '@/lib/i18n';
import Hero from '@/components/ui/Hero';
import Card, { CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { NavigationIcons } from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化案例卡片组件
const ModernCaseStudyCard = ({
  title,
  summary,
  industry,
  client,
  duration,
  metrics,
  tags,
  image,
  locale
}: {
  title: string;
  summary: string;
  industry: string;
  client: string;
  duration: string;
  metrics: Array<{ label: string; value: string; trend: 'up' | 'down' }>;
  tags: string[];
  image: string;
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Card
      className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200"
      padding="none"
      shadow="lg"
      border={false}
    >
      {/* 案例图片/图标 */}
      <div className="relative h-48 bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-grid opacity-20" />
        <div className="relative text-6xl">{image}</div>
        <div className="absolute top-4 right-4">
          <Badge variant="primary" size="sm">
            {industry}
          </Badge>
        </div>
      </div>

      <CardContent className="p-6">
        {/* 标题和描述 */}
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-neutral-900 mb-2 group-hover:text-primary-600 transition-colors">
            {title}
          </h3>
          <p className="text-neutral-600 text-sm leading-relaxed mb-4">
            {summary}
          </p>
          <div className="flex items-center text-sm text-neutral-500 space-x-4">
            <span>{locale === 'zh' ? '客户' : 'Client'}: {client}</span>
            <span>{locale === 'zh' ? '周期' : 'Duration'}: {duration}</span>
          </div>
        </div>

        {/* 关键指标 */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-neutral-700 mb-3">
            {locale === 'zh' ? '关键成果' : 'Key Results'}
          </h4>
          <div className="grid grid-cols-2 gap-3">
            {metrics.map((metric, index) => (
              <div key={index} className="bg-neutral-50 rounded-lg p-3 text-center">
                <div className={`text-lg font-bold ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.value}
                </div>
                <div className="text-xs text-neutral-500">{metric.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* 技术标签 */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <Badge key={index} variant="outline" size="sm">
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* 查看详情按钮 */}
        <Button
          variant="outline"
          size="sm"
          fullWidth
          className="group-hover:border-primary-600 group-hover:text-primary-600"
          icon={NavigationIcons.Arrow}
          iconPosition="right"
        >
          {locale === 'zh' ? '查看详情' : 'View Details'}
        </Button>
      </CardContent>
    </Card>
  </motion.div>
);

export default function CaseStudies() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');

  // 行业分类
  const industries = [
    { id: 'all', name: isZh ? '全部' : 'All' },
    { id: 'ecommerce', name: isZh ? '电商零售' : 'E-commerce' },
    { id: 'fintech', name: isZh ? '金融科技' : 'FinTech' },
    { id: 'education', name: isZh ? '教育科技' : 'EdTech' },
    { id: 'healthcare', name: isZh ? '医疗健康' : 'Healthcare' },
    { id: 'enterprise', name: isZh ? '企业服务' : 'Enterprise' }
  ];

  // 客户案例数据
  const cases = [
    {
      title: isZh ? '跨境电商增长系统' : 'Cross-border E-commerce Platform',
      summary: isZh
        ? '为某知名跨境电商平台重构了整个用户购买流程，通过数据驱动的 A/B 测试和性能优化，显著提升了转化率和用户体验。'
        : 'Rebuilt the entire user purchase flow for a leading cross-border e-commerce platform. Through data-driven A/B testing and performance optimization, significantly improved conversion rates and user experience.',
      industry: 'ecommerce',
      client: isZh ? '某跨境电商平台' : 'Global E-commerce Platform',
      duration: isZh ? '6 个月' : '6 months',
      metrics: [
        { label: isZh ? '转化率' : 'Conversion Rate', value: '+28%', trend: 'up' as const },
        { label: isZh ? '用户留存' : 'User Retention', value: '+18%', trend: 'up' as const },
        { label: isZh ? '页面加载' : 'Page Load', value: '-45%', trend: 'down' as const },
        { label: isZh ? '故障率' : 'Error Rate', value: '-42%', trend: 'down' as const },
      ],
      tags: ['Next.js', 'Node.js', 'Redis', 'A/B Testing'],
      image: '🛒'
    },
    {
      title: isZh ? '智能投资理财平台' : 'Smart Investment Platform',
      summary: isZh
        ? '为某金融科技公司开发了智能投资理财平台，集成了风险评估、智能推荐和实时交易功能，帮助用户做出更明智的投资决策。'
        : 'Developed a smart investment platform for a fintech company, integrating risk assessment, intelligent recommendations, and real-time trading features to help users make smarter investment decisions.',
      industry: 'fintech',
      client: isZh ? '某金融科技公司' : 'FinTech Startup',
      duration: isZh ? '8 个月' : '8 months',
      metrics: [
        { label: isZh ? '用户增长' : 'User Growth', value: '+156%', trend: 'up' as const },
        { label: isZh ? '交易量' : 'Trading Volume', value: '+89%', trend: 'up' as const },
        { label: isZh ? '风险控制' : 'Risk Control', value: '+95%', trend: 'up' as const },
        { label: isZh ? '客户满意度' : 'CSAT', value: '4.8/5', trend: 'up' as const },
      ],
      tags: ['React', 'Python', 'TensorFlow', 'WebSocket'],
      image: '💰'
    },
    {
      title: isZh ? '在线教育学习平台' : 'Online Learning Platform',
      summary: isZh
        ? '为某教育科技公司构建了完整的在线学习平台，支持直播教学、作业管理、学习进度跟踪等功能，服务了数万名学生和教师。'
        : 'Built a comprehensive online learning platform for an EdTech company, supporting live teaching, homework management, and learning progress tracking, serving tens of thousands of students and teachers.',
      industry: 'education',
      client: isZh ? '某教育科技公司' : 'EdTech Company',
      duration: isZh ? '10 个月' : '10 months',
      metrics: [
        { label: isZh ? '活跃用户' : 'Active Users', value: '+200%', trend: 'up' as const },
        { label: isZh ? '完课率' : 'Completion Rate', value: '+65%', trend: 'up' as const },
        { label: isZh ? '教学效果' : 'Learning Outcome', value: '+40%', trend: 'up' as const },
        { label: isZh ? '系统稳定性' : 'Uptime', value: '99.9%', trend: 'up' as const },
      ],
      tags: ['Vue.js', 'WebRTC', 'Socket.io', 'MongoDB'],
      image: '📚'
    },
    {
      title: isZh ? '智慧医疗管理系统' : 'Smart Healthcare Management',
      summary: isZh
        ? '为某三甲医院开发了智慧医疗管理系统，整合了患者管理、医生排班、药品库存等功能，大幅提升了医院的运营效率。'
        : 'Developed a smart healthcare management system for a top-tier hospital, integrating patient management, doctor scheduling, and pharmaceutical inventory, significantly improving hospital operational efficiency.',
      industry: 'healthcare',
      client: isZh ? '某三甲医院' : 'Top-tier Hospital',
      duration: isZh ? '12 个月' : '12 months',
      metrics: [
        { label: isZh ? '效率提升' : 'Efficiency', value: '+75%', trend: 'up' as const },
        { label: isZh ? '等待时间' : 'Wait Time', value: '-60%', trend: 'down' as const },
        { label: isZh ? '患者满意度' : 'Patient Satisfaction', value: '+85%', trend: 'up' as const },
        { label: isZh ? '成本节约' : 'Cost Savings', value: '+30%', trend: 'up' as const },
      ],
      tags: ['Angular', 'Java', 'PostgreSQL', 'Docker'],
      image: '🏥'
    },
    {
      title: isZh ? '企业数字化转型平台' : 'Enterprise Digital Transformation',
      summary: isZh
        ? '为某大型制造企业构建了数字化转型平台，整合了 ERP、CRM、供应链管理等系统，实现了企业全流程的数字化管理。'
        : 'Built a digital transformation platform for a large manufacturing enterprise, integrating ERP, CRM, and supply chain management systems to achieve end-to-end digital management.',
      industry: 'enterprise',
      client: isZh ? '某制造企业' : 'Manufacturing Enterprise',
      duration: isZh ? '15 个月' : '15 months',
      metrics: [
        { label: isZh ? '流程效率' : 'Process Efficiency', value: '+120%', trend: 'up' as const },
        { label: isZh ? '数据准确性' : 'Data Accuracy', value: '+95%', trend: 'up' as const },
        { label: isZh ? '运营成本' : 'Operating Cost', value: '-35%', trend: 'down' as const },
        { label: isZh ? '决策速度' : 'Decision Speed', value: '+80%', trend: 'up' as const },
      ],
      tags: ['Microservices', 'Kubernetes', 'React', 'Spring Boot'],
      image: '🏭'
    },
    {
      title: isZh ? '智能物流配送系统' : 'Smart Logistics System',
      summary: isZh
        ? '为某物流公司开发了智能配送系统，通过 AI 算法优化配送路线，实时跟踪货物状态，大幅提升了配送效率和客户满意度。'
        : 'Developed a smart delivery system for a logistics company, optimizing delivery routes through AI algorithms and real-time cargo tracking, significantly improving delivery efficiency and customer satisfaction.',
      industry: 'enterprise',
      client: isZh ? '某物流公司' : 'Logistics Company',
      duration: isZh ? '7 个月' : '7 months',
      metrics: [
        { label: isZh ? '配送效率' : 'Delivery Efficiency', value: '+90%', trend: 'up' as const },
        { label: isZh ? '燃油成本' : 'Fuel Cost', value: '-25%', trend: 'down' as const },
        { label: isZh ? '准时率' : 'On-time Rate', value: '+95%', trend: 'up' as const },
        { label: isZh ? '客户满意度' : 'Customer Satisfaction', value: '+70%', trend: 'up' as const },
      ],
      tags: ['Python', 'TensorFlow', 'Redis', 'GPS API'],
      image: '🚚'
    }
  ];

  // 根据选中的行业筛选案例
  const filteredCases = selectedIndustry === 'all'
    ? cases
    : cases.filter(c => c.industry === selectedIndustry);

  return (
    <>
      {/* Hero 部分 */}
      <Hero
        title={isZh ? '客户成功案例' : 'Client Success Stories'}
        subtitle={isZh
          ? '以结果为导向的工程实践，用数据说话的成功案例，展示我们如何帮助客户实现业务目标'
          : 'Result-driven engineering practices with data-backed success stories, showcasing how we help clients achieve their business goals'}
        align="center"
      />

      {/* 行业筛选 */}
      <section className="py-12 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-8"
          >
            <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
              {isZh ? '按行业浏览' : 'Browse by Industry'}
            </h2>
            <div className="flex flex-wrap justify-center gap-3">
              {industries.map((industry) => (
                <Button
                  key={industry.id}
                  variant={selectedIndustry === industry.id ? "primary" : "outline"}
                  size="sm"
                  onClick={() => setSelectedIndustry(industry.id)}
                  className="transition-all duration-200"
                >
                  {industry.name}
                </Button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* 案例展示 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredCases.map((caseStudy) => (
              <ModernCaseStudyCard
                key={caseStudy.title}
                {...caseStudy}
                locale={localeTyped}
              />
            ))}
          </motion.div>

          {filteredCases.length === 0 && (
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-medium text-neutral-900 mb-2">
                {isZh ? '暂无相关案例' : 'No Cases Found'}
              </h3>
              <p className="text-neutral-600">
                {isZh ? '该行业的案例正在整理中，敬请期待' : 'Cases for this industry are being organized, stay tuned'}
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* 统计数据部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '我们的成果' : 'Our Achievements'}
            </h2>
            <p className="text-xl text-primary-100">
              {isZh ? '用数据证明我们的专业能力' : 'Proving our expertise with data'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-4 gap-8"
          >
            {[
              { number: '50+', label: isZh ? '成功项目' : 'Successful Projects' },
              { number: '98%', label: isZh ? '客户满意度' : 'Client Satisfaction' },
              { number: '2.5x', label: isZh ? '平均性能提升' : 'Avg Performance Boost' },
              { number: '24/7', label: isZh ? '技术支持' : 'Technical Support' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center"
              >
                <div className="text-4xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-primary-100">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '准备开始您的成功故事？' : 'Ready to Start Your Success Story?'}
            </h2>
            <p className="text-xl text-neutral-600 mb-8">
              {isZh
                ? '让我们一起创造下一个令人瞩目的成功案例'
                : 'Let\'s create the next remarkable success story together'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="primary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '开始合作' : 'Start Collaboration'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/services`}
              >
                {isZh ? '了解服务' : 'Learn About Services'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}

