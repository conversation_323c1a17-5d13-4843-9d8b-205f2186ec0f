'use client';
import React from 'react';
import { motion } from "framer-motion";
import { useParams } from 'next/navigation';
import { type Locale } from '@/lib/i18n';
import Hero from '@/components/ui/Hero';
import Card, { CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { NavigationIcons } from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化价格卡片组件
const ModernPricingCard = ({
  name,
  price,
  period,
  description,
  features,
  isPopular = false,
  ctaText,
  onSelect,
  locale
}: {
  name: string;
  price: string;
  period?: string;
  description: string;
  features: string[];
  isPopular?: boolean;
  ctaText: string;
  onSelect: () => void;
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="relative"
  >
    {isPopular && (
      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
        <Badge variant="primary" size="sm">
          {locale === 'zh' ? '最受欢迎' : 'Most Popular'}
        </Badge>
      </div>
    )}
    <Card
      className={`h-full transition-all duration-300 hover:scale-105 ${
        isPopular
          ? 'border-2 border-primary-200 shadow-xl bg-gradient-to-b from-white to-primary-50/30'
          : 'border border-neutral-200 hover:border-primary-200 hover:shadow-lg'
      }`}
      padding="none"
      shadow="none"
      border={false}
    >
      <CardHeader className="p-8 pb-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-neutral-900 mb-2">{name}</h3>
          <p className="text-neutral-600 text-sm mb-6">{description}</p>
          <div className="mb-6">
            <div className="flex items-baseline justify-center">
              <span className="text-4xl font-bold text-neutral-900">{price}</span>
              {period && <span className="text-neutral-500 ml-2">{period}</span>}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-8 pt-0">
        <ul className="space-y-4 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <NavigationIcons.CheckCircle className="w-5 h-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
              <span className="text-neutral-700 text-sm">{feature}</span>
            </li>
          ))}
        </ul>
        <Button
          variant={isPopular ? "primary" : "outline"}
          size="lg"
          fullWidth
          onClick={onSelect}
          className="font-medium"
        >
          {ctaText}
        </Button>
      </CardContent>
    </Card>
  </motion.div>
);

export default function Pricing() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  // 价格方案数据
  const plans = [
    {
      name: isZh ? '基础版' : 'Starter',
      price: isZh ? '¥8,000' : '$1,200',
      period: isZh ? '起' : 'from',
      description: isZh ? '适合初创企业和个人项目' : 'Perfect for startups and personal projects',
      features: [
        isZh ? '响应式企业官网' : 'Responsive company website',
        isZh ? 'SEO 基础优化' : 'Basic SEO optimization',
        isZh ? '移动端适配' : 'Mobile optimization',
        isZh ? '基础动效交互' : 'Basic animations',
        isZh ? '7 天快速上线' : '7-day quick launch',
        isZh ? '3 个月技术支持' : '3 months technical support'
      ],
      ctaText: isZh ? '选择基础版' : 'Choose Starter',
      isPopular: false
    },
    {
      name: isZh ? '专业版' : 'Professional',
      price: isZh ? '¥18,000' : '$2,800',
      period: isZh ? '起' : 'from',
      description: isZh ? '适合成长型企业和复杂项目' : 'Ideal for growing businesses and complex projects',
      features: [
        isZh ? '多语言国际化支持' : 'Multi-language i18n support',
        isZh ? 'SSR 服务端渲染' : 'Server-side rendering (SSR)',
        isZh ? '后台管理系统' : 'Admin management system',
        isZh ? 'API 接口开发' : 'Custom API development',
        isZh ? '性能深度优化' : 'Advanced performance optimization',
        isZh ? '数据分析集成' : 'Analytics integration',
        isZh ? '6 个月技术支持' : '6 months technical support'
      ],
      ctaText: isZh ? '选择专业版' : 'Choose Professional',
      isPopular: true
    },
    {
      name: isZh ? '企业定制版' : 'Enterprise',
      price: isZh ? '面议' : 'Custom',
      description: isZh ? '为大型企业量身定制的解决方案' : 'Tailored solutions for large enterprises',
      features: [
        isZh ? '从 0 到 1 产品定制开发' : 'End-to-end product development',
        isZh ? '微服务架构设计' : 'Microservices architecture',
        isZh ? '云原生部署方案' : 'Cloud-native deployment',
        isZh ? 'DevOps 自动化流程' : 'DevOps automation pipeline',
        isZh ? 'A/B 测试与实验平台' : 'A/B testing & experimentation',
        isZh ? '7×24 技术支持' : '24/7 technical support',
        isZh ? '专属技术顾问' : 'Dedicated technical consultant'
      ],
      ctaText: isZh ? '联系我们' : 'Contact Us',
      isPopular: false
    }
  ];

  // 客户评价数据
  const testimonials = [
    {
      quote: isZh
        ? '"三娃团队的交付速度和代码质量都超出了我们的预期，项目按时上线并获得了用户的好评。"'
        : '"Sanva team\'s delivery speed and code quality exceeded our expectations. The project launched on time and received great user feedback."',
      author: isZh ? '张总' : 'Zhang CEO',
      role: isZh ? '某科技公司创始人' : 'Tech Company Founder',
      company: isZh ? '创新科技' : 'Innovation Tech'
    },
    {
      quote: isZh
        ? '"在移动端用户体验和性能优化方面非常专业，帮助我们的应用在应用商店获得了 4.8 分的高评分。"'
        : '"Very professional in mobile UX and performance optimization. Helped our app achieve a 4.8-star rating in app stores."',
      author: isZh ? '李经理' : 'Li Manager',
      role: isZh ? '产品经理' : 'Product Manager',
      company: isZh ? '移动互联' : 'Mobile Connect'
    }
  ];

  return (
    <>
      {/* Hero 部分 */}
      <Hero
        title={isZh ? '透明的价格方案' : 'Transparent Pricing Plans'}
        subtitle={isZh
          ? '选择最适合您项目需求的服务方案，所有价格公开透明，无隐藏费用'
          : 'Choose the service plan that best fits your project needs. All prices are transparent with no hidden fees'}
        align="center"
      />

      {/* 价格方案部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid lg:grid-cols-3 gap-8"
          >
            {plans.map((plan) => (
              <ModernPricingCard
                key={plan.name}
                {...plan}
                locale={localeTyped}
                onSelect={() => {
                  window.location.href = `/${locale}/contact`;
                }}
              />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 功能对比部分 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '详细功能对比' : 'Detailed Feature Comparison'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '了解每个方案包含的具体功能和服务，帮助您做出最佳选择'
                : 'Understand the specific features and services included in each plan to help you make the best choice'}
            </p>
          </motion.div>

          {/* 功能对比表格 */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="bg-white rounded-2xl shadow-sm border border-neutral-200 overflow-hidden"
          >
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-neutral-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-neutral-900">
                      {isZh ? '功能特性' : 'Features'}
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-medium text-neutral-900">
                      {isZh ? '基础版' : 'Starter'}
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-medium text-neutral-900 bg-primary-50">
                      {isZh ? '专业版' : 'Professional'}
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-medium text-neutral-900">
                      {isZh ? '企业版' : 'Enterprise'}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-neutral-200">
                  {[
                    {
                      feature: isZh ? '响应式设计' : 'Responsive Design',
                      starter: true,
                      pro: true,
                      enterprise: true
                    },
                    {
                      feature: isZh ? 'SEO 优化' : 'SEO Optimization',
                      starter: true,
                      pro: true,
                      enterprise: true
                    },
                    {
                      feature: isZh ? '多语言支持' : 'Multi-language Support',
                      starter: false,
                      pro: true,
                      enterprise: true
                    },
                    {
                      feature: isZh ? '后台管理系统' : 'Admin System',
                      starter: false,
                      pro: true,
                      enterprise: true
                    },
                    {
                      feature: isZh ? 'API 开发' : 'API Development',
                      starter: false,
                      pro: true,
                      enterprise: true
                    },
                    {
                      feature: isZh ? '云原生部署' : 'Cloud-native Deployment',
                      starter: false,
                      pro: false,
                      enterprise: true
                    },
                    {
                      feature: isZh ? '专属技术顾问' : 'Dedicated Consultant',
                      starter: false,
                      pro: false,
                      enterprise: true
                    }
                  ].map((row, index) => (
                    <tr key={index} className="hover:bg-neutral-50">
                      <td className="px-6 py-4 text-sm text-neutral-900">{row.feature}</td>
                      <td className="px-6 py-4 text-center">
                        {row.starter ? (
                          <NavigationIcons.CheckCircle className="w-5 h-5 text-primary-600 mx-auto" />
                        ) : (
                          <span className="text-neutral-400">—</span>
                        )}
                      </td>
                      <td className="px-6 py-4 text-center bg-primary-50/50">
                        {row.pro ? (
                          <NavigationIcons.CheckCircle className="w-5 h-5 text-primary-600 mx-auto" />
                        ) : (
                          <span className="text-neutral-400">—</span>
                        )}
                      </td>
                      <td className="px-6 py-4 text-center">
                        {row.enterprise ? (
                          <NavigationIcons.CheckCircle className="w-5 h-5 text-primary-600 mx-auto" />
                        ) : (
                          <span className="text-neutral-400">—</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      </section>

      {/* 客户评价部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '客户怎么说' : 'What Our Clients Say'}
            </h2>
            <p className="text-xl text-neutral-600">
              {isZh ? '听听我们客户的真实反馈' : 'Hear from our satisfied clients'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 gap-8"
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="bg-neutral-50 rounded-2xl p-8"
              >
                <div className="mb-6">
                  <p className="text-lg text-neutral-700 leading-relaxed">
                    {testimonial.quote}
                  </p>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-primary-600 font-semibold">
                      {testimonial.author.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-neutral-900">{testimonial.author}</div>
                    <div className="text-sm text-neutral-600">
                      {testimonial.role} · {testimonial.company}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '准备开始您的项目了吗？' : 'Ready to Start Your Project?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '联系我们获取免费咨询，让我们帮助您实现数字化目标'
                : 'Contact us for a free consultation and let us help you achieve your digital goals'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '免费咨询' : 'Free Consultation'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/portfolio`}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                {isZh ? '查看案例' : 'View Portfolio'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}

