# GitLab CI/CD 配置指南

## 环境变量配置

在 GitLab 项目中，需要配置以下环境变量：

### 必需的环境变量

1. **SSH_PRIVATE_KEY** (Type: Variable, Protected: Yes, Masked: Yes)
   ```
   -----BEGIN OPENSSH PRIVATE KEY-----
   [您的私钥内容]
   -----END OPENSSH PRIVATE KEY-----
   ```
   > 这是您的 `~/.ssh/gitlab_ssh_id` 文件的内容

### 配置步骤

#### 1. 获取 SSH 私钥内容
```bash
# 在本地执行，复制输出内容
cat ~/.ssh/gitlab_ssh_id
```

#### 2. 在 GitLab 中配置环境变量
1. 进入项目 → Settings → CI/CD
2. 展开 Variables 部分
3. 点击 "Add variable"
4. 配置如下：
   - **Key**: `SSH_PRIVATE_KEY`
   - **Value**: 粘贴私钥内容
   - **Type**: Variable
   - **Environment scope**: All (default)
   - **Protect variable**: ✅ (勾选)
   - **Mask variable**: ✅ (勾选)

#### 3. 验证服务器连接
确保以下命令在本地可以正常执行：
```bash
ssh -i ~/.ssh/gitlab_ssh_id root@************
```

## 服务器准备

### 1. 在服务器上安装必要软件
```bash
# 连接到服务器
ssh -i ~/.ssh/gitlab_ssh_id root@************

# 安装 Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
apt-get install -y nodejs

# 安装 PM2
npm install -g pm2

# 安装 Git
apt-get update && apt-get install -y git

# 创建部署目录
mkdir -p /var/www/sanva-website
chown -R root:root /var/www/sanva-website
```

### 2. 配置 PM2 开机自启
```bash
# 设置 PM2 开机自启
pm2 startup
pm2 save
```

## CI/CD 流程说明

### 流程阶段
1. **deploy** - 远程构建和部署
2. **health-check** - 健康检查

### 部署架构
- **Eva 节点**: 作为跳板机，负责 SSH 连接和命令执行
- **远程服务器**: 直接拉取代码、构建和部署，避免架构兼容性问题

### 触发条件
- **自动部署**: 当提交信息以 `release:` 开头时，自动执行远程构建和部署流程
- **手动部署**: 普通提交需要手动点击 "Deploy" 按钮进行生产部署
- **回滚**: 可手动触发回滚到上一个版本

### 自动部署规则
- ✅ **自动部署**: `git commit -m "release: 发布新版本 v1.2.0"`
- ✅ **自动部署**: `git commit -m "release: 修复重要bug"`
- ❌ **手动部署**: `git commit -m "feat: 添加新功能"`
- ❌ **手动部署**: `git commit -m "fix: 修复小问题"`

### 部署特性
- ✅ 零停机部署（使用 PM2 reload）
- ✅ 自动备份多个版本（保留最近3个）
- ✅ 健康检查
- ✅ 一键回滚
- ✅ 远程构建，避免架构兼容性问题
- ✅ 详细的部署日志
- ✅ 支持增量更新和完整克隆
- ✅ Eva 节点作为跳板，资源占用最小

## 使用方法

### 自动部署流程（推荐）
1. 提交代码时使用 `release:` 前缀：
   ```bash
   git add .
   git commit -m "release: 发布新版本 v1.2.0"
   git push origin main
   ```
2. 系统将自动执行：远程拉取代码 → 构建 → 部署 → 健康检查
3. 无需手动干预，全自动完成部署

### 手动部署流程
1. 普通提交推送到 `main` 分支：
   ```bash
   git commit -m "feat: 添加新功能"
   git push origin main
   ```
2. 在 GitLab CI/CD → Pipelines 中点击 "Deploy" 按钮
3. 等待远程构建和部署完成
4. 等待健康检查通过

### 提交信息示例

**自动部署的提交信息（以 `release:` 开头）：**
```bash
git commit -m "release: 发布 v2.1.0 版本"
git commit -m "release: 紧急修复支付bug"
git commit -m "release: 更新产品页面设计"
git commit -m "release: 优化网站性能"
```

**手动部署的提交信息（其他格式）：**
```bash
git commit -m "feat: 添加新的联系表单"
git commit -m "fix: 修复导航菜单样式"
git commit -m "docs: 更新README文档"
git commit -m "refactor: 重构组件结构"
```

### 紧急回滚
1. 在 GitLab CI/CD → Pipelines 中找到最新的 pipeline
2. 点击 "Rollback" 按钮
3. 确认回滚操作

### 查看部署状态
```bash
# 连接到服务器查看状态
ssh -i ~/.ssh/gitlab_ssh_id root@************

# 查看 PM2 状态
pm2 list

# 查看应用日志
pm2 logs sanva-website

# 查看服务状态
curl http://localhost:55300
```

## 故障排除

### 常见问题

1. **SSH 连接失败**
   - 检查 SSH_PRIVATE_KEY 环境变量是否正确配置
   - 确认服务器 IP 和用户名正确
   - 检查服务器 SSH 服务是否正常

2. **部署失败**
   - 查看 CI/CD 日志中的详细错误信息
   - 检查服务器磁盘空间是否充足
   - 确认 Node.js 和 PM2 已正确安装

3. **健康检查失败**
   - 检查应用是否正常启动
   - 查看 PM2 日志：`pm2 logs sanva-website`
   - 检查端口 55300 是否被占用

### 调试命令
```bash
# 在服务器上手动测试部署流程
cd /var/www/sanva-website
npm run build
pm2 reload ecosystem.config.js --env production
pm2 list
curl http://localhost:55300
```

## 安全注意事项

1. SSH 私钥已设置为 Protected 和 Masked
2. 只有 main 分支可以触发生产部署
3. 部署需要手动确认
4. 所有敏感信息都通过环境变量管理

## 监控和维护

建议定期检查：
- 服务器磁盘空间
- PM2 进程状态
- 应用日志
- 系统资源使用情况

```bash
# 服务器维护命令
df -h                    # 检查磁盘空间
pm2 monit               # PM2 监控界面
pm2 logs --lines 100    # 查看最近日志
top                     # 系统资源监控
```
